from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver
from device_manager.models import Device
from channels.layers import get_channel_layer
from django.dispatch import receiver
from asgiref.sync import async_to_sync
from django.conf import settings

# Optional webpush import
try:
    from webpush import send_user_notification
except ImportError:
    send_user_notification = None

class Event(models.Model):
    EVENT_TYPES = [
        ("Danger", "Danger"),
        ("Warning", "Warning"),
        ("Update", "Update"),
        ("Info", "Info"),
    ]

    devi = models.ForeignKey(Device, on_delete=models.CASCADE)
    type = models.CharField(
        max_length=8, choices=EVENT_TYPES, help_text="Type of the Event"
    )
    desc = models.CharField(max_length=255, help_text="Description of the Event")
    crat = models.DateTimeField(auto_now_add=True, help_text="Datetime of the Event")

    def __str__(self):
        return f"{self.type}: {self.devi.name} {self.desc}"

    def to_dict(self):
        formatted_crat = self.crat.strftime("%d %B %Y — %I:%M:%S %p")
        return {
            "id": self.id,
            "type": self.type,
            "desc": self.desc,
            "devi": {
                "id": self.devi.id,
                "name": self.devi.name,
            },
            "crat": formatted_crat,
        }


class Notification(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    evnt = models.ForeignKey(
        Event, on_delete=models.CASCADE, related_name="notifications"
    )
    read = models.BooleanField(default=False)
    sent = models.BooleanField(
        default=False,
        help_text="Whether the notification was sent through a route or not.",
    )
    fail = models.CharField(
        default="No Error.", help_text="Reason behind delivery failure."
    )

    def to_dict(self):
        return {
            "id": self.id,
            "user": self.user.username,
            "evnt": self.evnt.to_dict(),
        }


class NotificationSettings(models.Model):
    DELIVERY_METHODS = [
        ("No Delivery", "No Delivery"),
        ("Email", "Email"),
        ("SMS", "SMS"),
        ("Email and SMS", "Email and SMS"),
    ]
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    rxif = models.BooleanField(default=True, help_text="Receive Information")
    rxup = models.BooleanField(default=True, help_text="Receive Updates")
    rxwr = models.BooleanField(default=True, help_text="Receive Warnings")
    rxdg = models.BooleanField(default=True, help_text="Receive Dangers")
    devs = models.ManyToManyField(Device, blank=True, help_text="Receive from Devices")
    mthd = models.CharField(
        max_length=16,
        default="No Delivery",
        choices=DELIVERY_METHODS,
        help_text="Notification delivery method.",
    )

    def __str__(self):
        return self.user.username


@receiver(post_save, sender=Event)
def create_notifications_and_send_updates(sender, instance: Event, created, **kwargs):
    if created:
        devi = instance.devi
        instance_dict = instance.to_dict()

        # Get the accessible users
        accessible_users = User.objects.filter(userprofile__devs=devi)

        # update device events groups
        async_to_sync(get_channel_layer().group_send)(
            f"deviceEvents_{devi.id}",
            {
                "type": "object_update",
                "data": instance_dict,
            },
        )

        # Create notifications for accessible users
        for user in accessible_users:
            notification = Notification.objects.create(user=user, evnt=instance)

            # update user notifications groups
            async_to_sync(get_channel_layer().group_send)(
                f"notifications_{user.id}",
                {
                    "type": "object_update",
                    "data": notification.to_dict(),
                },
            )

            # send push notifications
            payload = {
                "head": instance.type,
                "body": instance_dict["devi"]["name"] + " " + instance_dict["desc"],
                "icon": "https://"
                + settings.WHISKERS_HUB_DOMAIN
                + "/static/images/ic-whiskershub/res/mipmap-xxhdpi/ic-whiskershub.png",
                "url": "https://"
                + settings.WHISKERS_HUB_DOMAIN
                + "/device/"
                + str(devi.id),
            }
            send_user_notification(user=user, payload=payload, ttl=1000)
