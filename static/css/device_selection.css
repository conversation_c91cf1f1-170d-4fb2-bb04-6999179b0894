/* Device Selection Widget Styles */
.device-selection-widget {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1.5rem;
    background-color: #f8f9fc;
}

.device-selection-widget .form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.field-filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.field-filter-btn {
    transition: all 0.2s ease-in-out;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.field-filter-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.field-filter-btn.active {
    background-color: #4e73df !important;
    border-color: #4e73df !important;
    color: white !important;
}

#device-selection-table {
    margin-top: 1rem;
    background-color: white;
    border-radius: 0.35rem;
    overflow: hidden;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

#device-selection-table thead th {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    color: #5a5c69;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

#device-selection-table tbody tr {
    transition: background-color 0.15s ease-in-out;
}

#device-selection-table tbody tr:hover {
    background-color: #f8f9fc;
}

#device-selection-table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #e3e6f0;
    font-size: 0.875rem;
}

.device-checkbox {
    transform: scale(1.2);
    margin: 0;
}

.device-checkbox:checked {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* Action buttons styling */
.device-selection-widget .btn-outline-primary {
    color: #4e73df;
    border-color: #4e73df;
}

.device-selection-widget .btn-outline-primary:hover {
    background-color: #4e73df;
    border-color: #4e73df;
    color: white;
}

.device-selection-widget .btn-outline-secondary {
    color: #858796;
    border-color: #858796;
}

.device-selection-widget .btn-outline-secondary:hover {
    background-color: #858796;
    border-color: #858796;
    color: white;
}

/* Responsive design */
@media (max-width: 768px) {
    .field-filter-buttons {
        justify-content: center;
    }
    
    .field-filter-btn {
        flex: 1;
        min-width: 120px;
    }
    
    #device-selection-table {
        font-size: 0.8rem;
    }
    
    #device-selection-table thead th,
    #device-selection-table tbody td {
        padding: 0.5rem;
    }
}

/* Table info styling */
.device-table-info {
    font-size: 0.875rem;
    color: #858796;
    margin-top: 0.5rem;
    text-align: right;
}

/* Loading state */
.device-selection-widget.loading {
    opacity: 0.7;
    pointer-events: none;
}

.device-selection-widget.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4e73df;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty state */
.device-selection-empty {
    text-align: center;
    padding: 2rem;
    color: #858796;
}

.device-selection-empty i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}
